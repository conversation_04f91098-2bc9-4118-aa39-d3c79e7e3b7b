class UIMenu {
    constructor() {
        this.d = {
            europe: [
                { name: 'Vend<PERSON>', desc: 'FFA' }, { name: '<PERSON><PERSON><PERSON>', desc: 'Experimental' },
                { name: '<PERSON>', desc: 'Experimental' }, { name: '<PERSON>', desc: 'Instant' },
                { name: '<PERSON><PERSON>', desc: 'Self Feed' }, { name: 'Underworld', desc: 'Mega Split' },
                { name: 'Sedulous', desc: 'Gun Split' }, { name: 'Storm', desc: 'Ultra (256x)' },
            ],
            'north america': [
                { name: 'Avalon', desc: 'FFA' }, { name: '<PERSON>', desc: 'Experimental' },
                { name: '<PERSON>', desc: 'Instant' }, { name: 'Wreck', desc: 'Mega Split' },
            ],
        };
        this.s = { europe: -1, 'north america': -1 };
        this.r = 'europe';
        this.isSwitching = false;
        this.actionPanelsVisible = false;
        this.e = {
            body: document.body,
            panelWrapper: document.querySelector('.panel-wrapper'),
            regionBtn: document.getElementById('region-toggle-btn'),
            serverPanel: document.getElementById('server-panel'),
            serverListContainer: document.getElementById('server-list-container'),
            nameBoxInput: document.querySelector('.name-box input'),
            playerPanel: document.querySelector('.player-panel'),
            animatedIntroPanels: document.querySelectorAll('.top-bar, .panel-wrapper'),
        };
        this.i();
    }

    animateList(container, elements) {
        container.innerHTML = '';
        elements.forEach((el, i) => {
            container.appendChild(el);
            setTimeout(() => el.classList.add('visible'), 50 + (50 * i));
        });
        setTimeout(() => {
            const scrollContainer = this.e.serverPanel.querySelector('.scroll-container');
            const listContainer = scrollContainer.querySelector('.list-container');
            const containerPadding = parseFloat(getComputedStyle(scrollContainer).paddingTop) + parseFloat(getComputedStyle(scrollContainer).paddingBottom);
            const finalHeight = listContainer.scrollHeight + containerPadding;
            this.e.panelWrapper.style.setProperty('--panel-height', `${finalHeight}px`);
        }, 100);
    }

    u(r, a) {
        const t = this.d[r];
        if (!t) return;
        const numServers = t.length;
        const rowHeight = 70;
        const rowGap = 8;
        const containerPadding = 32;
        const predictiveHeight = (numServers * rowHeight) + ((numServers - 1) * rowGap) + containerPadding;
        this.e.panelWrapper.style.setProperty('--panel-height', `${predictiveHeight}px`);

        let skeletonHTML = '';
        for (let i = 0; i < numServers; i++) {
            skeletonHTML += `<div class="skeleton-row"><div class="skeleton-text long"></div><div class="skeleton-text short"></div></div>`;
        }
        this.e.serverListContainer.innerHTML = skeletonHTML;
        
        setTimeout(() => {
            const n = this.s[r];
            const serverElements = t.map((s, i) => this.c(s, i, n));
            this.animateList(this.e.serverListContainer, serverElements);
        }, a ? 1200 : 800);
    }

    c(s, i, a) {
        const r = document.createElement('div');
        r.className = 'server-row';
        if (i === a) r.classList.add('active');
        r.innerHTML = `<span class="server-text">${s.name}</span><span class="server-desc">${s.desc}</span>`;
        r.addEventListener('click', () => {
            if (this.isSwitching) return;
            this.s[this.r] = i;
            this.e.serverListContainer.querySelectorAll('.server-row').forEach(e => e.classList.remove('active'));
            r.classList.add('active');
        });
        return r;
    }

    setLockUI(locked) {
        this.e.body.classList.toggle('ui-locked', locked);
    }

    h() {
        if (this.isSwitching) return;
        this.isSwitching = true;
        this.setLockUI(true);
        this.e.regionBtn.classList.add('switching');
        this.e.regionBtn.textContent = 'Switching...';

        setTimeout(() => {
            this.r = this.r === 'europe' ? 'north america' : 'europe';
            const regionName = this.r.charAt(0).toUpperCase() + this.r.slice(1);
            this.e.regionBtn.textContent = regionName;
            
            this.u(this.r, true);

            this.e.regionBtn.classList.remove('switching');
            this.isSwitching = false;
            this.setLockUI(false);
        }, 1000);
    }

    _handleNameInput() {
        const name = this.e.nameBoxInput.value.trim();
        const shouldBeVisible = name !== '';

        if (shouldBeVisible && !this.actionPanelsVisible) {
            this.actionPanelsVisible = true;
            this.e.playerPanel.classList.add('visible');
        } else if (!shouldBeVisible && this.actionPanelsVisible) {
            this.actionPanelsVisible = false;
            this.e.playerPanel.classList.remove('visible');
        }
    }

    _playIntroAnimation() {
        this.e.animatedIntroPanels.forEach((el, i) => {
            setTimeout(() => {
                el.classList.add('visible');
            }, i * 120);
        });
        setTimeout(() => this.u(this.r, false), 200);
    }

    i() {
        feather.replace({ width: '22px', height: '22px', 'stroke-width': 1.5 });
        this._playIntroAnimation();
        this.e.nameBoxInput.addEventListener('input', () => this._handleNameInput());
        this.e.regionBtn.addEventListener('click', () => this.h());
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new UIMenu();
});