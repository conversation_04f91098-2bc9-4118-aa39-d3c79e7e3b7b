<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brave Layout Test</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="web/brave.css">
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div id="main" class="main-container">
        <div class="main-panel-wrapper">
            <div class="main-left left-column">
                <div class="top-bar visible">
                    <div class="header-bar">
                        <div class="name-box">
                            <input type="text" id="name-box" placeholder="Player Name">
                        </div>
                    </div>
                </div>
                <div class="player-panel visible">
                    <button class="action-btn" id="btn-play">Play</button>
                    <button class="action-btn" id="btn-spec">Spectate</button>
                </div>
            </div>
            
            <div id="main-right" class="right-column">
                <div class="top-bar visible">
                    <div class="header-bar">
                        <button class="region-btn" id="region-toggle-btn">Europe</button>
                        <button class="icon-button active" id="server-list-btn"><i data-feather="server"></i></button>
                    </div>
                </div>
                <div class="panel-wrapper visible">
                    <div class="server-panel panel" id="server-panel">
                        <div class="scroll-container">
                            <div class="list-container" id="server-list-container">
                                <div class="server-row visible active">
                                    <span class="server-text">Vendetta</span>
                                    <span class="server-desc">FFA</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Fluffy</span>
                                    <span class="server-desc">Experimental</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Zeus</span>
                                    <span class="server-desc">Experimental</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Diamond</span>
                                    <span class="server-desc">Instant</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Bolt</span>
                                    <span class="server-desc">Self Feed</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Underworld</span>
                                    <span class="server-desc">Mega Split</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Sedulous</span>
                                    <span class="server-desc">Gun Split</span>
                                </div>
                                <div class="server-row visible">
                                    <span class="server-text">Storm</span>
                                    <span class="server-desc">Ultra (256x)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-buttons">
        <button class="icon-button" id="btn-options"><i data-feather="settings"></i></button>
        <button class="icon-button" id="btn-hotkeys"><i data-feather="command"></i></button>
        <button class="icon-button" id="btn-cellpanel"><i data-feather="grid"></i></button>
    </div>

    <script>
        feather.replace({ width: '22px', height: '22px', 'stroke-width': 1.5 });
        
        // Adicionar funcionalidade de clique nos servidores
        $('.server-row').on('click', function() {
            $('.server-row').removeClass('active');
            $(this).addClass('active');
        });
    </script>
</body>
</html>